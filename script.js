// Gamified Habit Tracker JavaScript

// Timer functionality
let timerInterval;
let timeLeft = 25 * 60; // 25 minutes in seconds
let isRunning = false;

function updateTimerDisplay() {
    const minutes = Math.floor(timeLeft / 60);
    const seconds = timeLeft % 60;
    const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.querySelector('.timer-display').textContent = display;
}

function startTimer() {
    if (!isRunning) {
        isRunning = true;
        timerInterval = setInterval(() => {
            timeLeft--;
            updateTimerDisplay();
            
            if (timeLeft <= 0) {
                clearInterval(timerInterval);
                isRunning = false;
                alert('🎉 Pomodoro completed! Great job!');
                timeLeft = 25 * 60; // Reset timer
                updateTimerDisplay();
            }
        }, 1000);
        
        document.querySelector('.timer-btn').textContent = 'Pause';
    } else {
        clearInterval(timerInterval);
        isRunning = false;
        document.querySelector('.timer-btn').textContent = 'Start';
    }
}

// Habit tracking functionality
function toggleHabit(habitElement) {
    habitElement.classList.toggle('completed');
    
    // Add visual feedback
    if (habitElement.classList.contains('completed')) {
        habitElement.style.transform = 'scale(1.05)';
        setTimeout(() => {
            habitElement.style.transform = 'scale(1)';
        }, 200);
        
        // Show completion message
        showNotification('✅ Habit completed! Keep it up!');
    }
}

// Notification system
function showNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: rgba(76, 175, 80, 0.9);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Add slide-in animation for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;
document.head.appendChild(style);

// Calendar functionality
function generateCalendar() {
    const calendarGrid = document.querySelector('.calendar-grid');
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    
    // Get first day of month and number of days
    const firstDay = new Date(currentYear, currentMonth, 1).getDay();
    const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
    
    // Clear existing calendar
    calendarGrid.innerHTML = '';
    
    // Add day headers
    const dayHeaders = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    dayHeaders.forEach(day => {
        const header = document.createElement('div');
        header.className = 'calendar-header-day';
        header.textContent = day;
        header.style.cssText = `
            font-weight: bold;
            text-align: center;
            padding: 10px 0;
            color: #ffd700;
        `;
        calendarGrid.appendChild(header);
    });
    
    // Add empty cells for days before month starts
    for (let i = 0; i < firstDay; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'calendar-day empty';
        calendarGrid.appendChild(emptyDay);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
        const dayElement = document.createElement('div');
        dayElement.className = 'calendar-day';
        dayElement.textContent = day;
        
        // Randomly mark some days as completed for demo
        if (Math.random() > 0.7) {
            dayElement.classList.add('completed');
        }
        
        // Highlight today
        if (day === today.getDate()) {
            dayElement.style.border = '2px solid #ffd700';
        }
        
        calendarGrid.appendChild(dayElement);
    }
}

// Level and progress simulation
function updateProgress() {
    const progressBars = document.querySelectorAll('.progress');
    progressBars.forEach(bar => {
        const currentWidth = parseInt(bar.style.width) || 0;
        const newWidth = Math.min(currentWidth + Math.random() * 5, 100);
        bar.style.width = newWidth + '%';
    });
}

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Initialize timer
    updateTimerDisplay();
    
    // Add timer event listener
    document.querySelector('.timer-btn').addEventListener('click', startTimer);
    
    // Generate calendar
    generateCalendar();
    
    // Add click handlers for habit cards
    document.querySelectorAll('.habit-card').forEach(card => {
        card.addEventListener('click', () => toggleHabit(card));
    });
    
    // Add click handlers for area cards
    document.querySelectorAll('.area-card').forEach(card => {
        card.addEventListener('click', () => {
            card.style.transform = 'scale(0.95)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
            showNotification(`📊 Viewing ${card.querySelector('.area-name').textContent} habits`);
        });
    });
    
    // Add click handlers for quick actions
    document.querySelectorAll('.action-item').forEach(item => {
        item.addEventListener('click', () => {
            showNotification(`🚀 ${item.textContent} action triggered!`);
        });
    });
    
    // Simulate progress updates every 30 seconds
    setInterval(updateProgress, 30000);
    
    // Add some interactive effects
    document.querySelectorAll('.widget').forEach(widget => {
        widget.addEventListener('mouseenter', () => {
            widget.style.transform = 'translateY(-2px)';
            widget.style.boxShadow = '0 8px 25px rgba(0,0,0,0.3)';
        });
        
        widget.addEventListener('mouseleave', () => {
            widget.style.transform = 'translateY(0)';
            widget.style.boxShadow = 'none';
        });
    });
    
    console.log('🎮 Gamified Habit Tracker initialized successfully!');
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Space bar to start/stop timer
    if (e.code === 'Space' && e.target.tagName !== 'INPUT') {
        e.preventDefault();
        startTimer();
    }
    
    // 'N' key to show new habit notification
    if (e.key === 'n' || e.key === 'N') {
        showNotification('➕ Ready to add a new habit!');
    }
});

// Export functions for potential future use
window.HabitTracker = {
    startTimer,
    toggleHabit,
    showNotification,
    generateCalendar,
    updateProgress
};
