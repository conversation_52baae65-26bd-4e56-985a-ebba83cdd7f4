* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    color: #ffffff;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    position: relative;
    height: 200px;
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), 
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 200"><rect fill="%23654321" width="800" height="200"/><rect fill="%238B4513" x="0" y="150" width="800" height="50"/><rect fill="%23FFD700" x="50" y="50" width="100" height="80"/><rect fill="%2332CD32" x="200" y="80" width="60" height="60"/><rect fill="%23FF6347" x="350" y="60" width="80" height="90"/></svg>');
    background-size: cover;
    border-radius: 15px;
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.title {
    font-size: 2.5rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    z-index: 2;
}

/* Main Layout */
.main-content {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 30px;
}

/* Sidebar */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.widget {
    background: rgba(30, 30, 50, 0.9);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.widget h3 {
    margin-bottom: 15px;
    font-size: 1.1rem;
    color: #ffd700;
}

/* Status Window */
.status-item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.avatar-section {
    display: flex;
    align-items: center;
    gap: 15px;
}

.avatar {
    width: 50px;
    height: 50px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    border-radius: 50%;
    border: 2px solid #ffd700;
}

.level {
    font-weight: bold;
    color: #ffd700;
}

.progress-bar {
    width: 100px;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background: linear-gradient(90deg, #4ecdc4, #44a08d);
    transition: width 0.3s ease;
}

/* Quick Action */
.action-item {
    padding: 10px;
    margin-bottom: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.action-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Timer */
.timer {
    text-align: center;
}

.timer-display {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ffd700;
    margin-bottom: 15px;
}

.timer-controls {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.timer-btn {
    padding: 8px 15px;
    background: rgba(255, 215, 0, 0.2);
    border: 1px solid #ffd700;
    border-radius: 6px;
    color: #ffd700;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timer-btn:hover {
    background: rgba(255, 215, 0, 0.3);
}

/* Rewards */
.reward-item {
    display: flex;
    gap: 15px;
}

.reward-image {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 8px;
}

.reward-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.reward-cost {
    color: #ffd700;
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.reward-desc {
    font-size: 0.8rem;
    color: #cccccc;
}

/* Main Content */
.content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.section {
    background: rgba(30, 30, 50, 0.9);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.section h2 {
    margin-bottom: 20px;
    font-size: 1.4rem;
    color: #ffd700;
}

/* Life Areas */
.area-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.area-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease, background 0.3s ease;
}

.area-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.1);
}

.area-icon {
    font-size: 2rem;
    margin-bottom: 10px;
}

.area-name {
    font-weight: bold;
}

/* Habit Controls */
.habit-controls {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.habit-controls label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.habit-controls input[type="checkbox"] {
    width: 16px;
    height: 16px;
}

/* Habits Grid */
.habits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.habit-card {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    display: flex;
    gap: 15px;
    transition: transform 0.3s ease;
}

.habit-card:hover {
    transform: translateY(-3px);
}

.habit-card.bad {
    border-left: 4px solid #ff6b6b;
}

.habit-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    background-size: cover;
    background-position: center;
}

.habit-image.deep-sleep { background: linear-gradient(45deg, #667eea, #764ba2); }
.habit-image.workout { background: linear-gradient(45deg, #f093fb, #f5576c); }
.habit-image.healthy-food { background: linear-gradient(45deg, #4facfe, #00f2fe); }
.habit-image.good-sleep { background: linear-gradient(45deg, #43e97b, #38f9d7); }
.habit-image.smoking { background: linear-gradient(45deg, #fa709a, #fee140); }
.habit-image.alcohol { background: linear-gradient(45deg, #a8edea, #fed6e3); }
.habit-image.fast-food { background: linear-gradient(45deg, #ff9a9e, #fecfef); }
.habit-image.high-stress { background: linear-gradient(45deg, #ffecd2, #fcb69f); }

.habit-name {
    font-weight: bold;
    margin-bottom: 5px;
}

.habit-streak, .habit-completion {
    font-size: 0.9rem;
    color: #cccccc;
    margin-bottom: 3px;
}

/* Calendar */
.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.calendar-day.completed {
    background: rgba(76, 175, 80, 0.3);
    border: 1px solid #4caf50;
}

.calendar-day:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.habit-card, .area-card, .widget {
    animation: fadeIn 0.6s ease-out;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .title {
        font-size: 2rem;
    }

    .habits-grid {
        grid-template-columns: 1fr;
    }

    .area-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .habit-controls {
        flex-direction: column;
        gap: 10px;
    }
}
